import React from "react";
import Image from "next/image";

const InfoCard = ({ title, description }) => {
  return (
    <div className="block w-full md:max-w-xl border border-pink-500  p-2 md:p-4 rounded-md shadow">
      <div className="w-full  flex items-start gap-1 mb-4">
        <div className="flex-shrink-0 w-6 h-6 sm:w-8 sm:h-8">
          <Image
            src="/Images/service_frame.png"
            alt="Tick"
            width={32}
            height={32}
            className="w-full h-full flex-shrink-0"
          />
        </div>
        <div className="w-[100%]">
          <h3 className="text-base md:text-lg font-semibold">{title}</h3>
          <p className="text-sm md:text-base text-justify">{description}</p>
        </div>
      </div>
    </div>
  );
};

const Section5 = () => {
  return (
    <section className="bg-blue-100 mb-10 md:mb-24 py-4 md:py-10">
      <div className="w-[85%] mx-auto ">
        <h2 className="text-xl md:text-3xl md:leading-[40px] text-center font-semibold">
          Benefits Of Hiring Valueans{" "}
          <span className="text-[#7716BC]">
            Dedicated Software Development Team{" "}
          </span>
           
        </h2>
        <div className="flex flex-col justify-center items-center gap-3 md:gap-6 my-3 md:my-6 ">
          <div className="flex flex-col md:flex-row justify-center md:justify-between items-center gap-3 md:gap-6">
            <InfoCard
              title={"You can disregard the costly hiring procedure."}
              description={
                "Hiring a specialized development team has a clear cost since we handle the selection, screening, and team building of IT professionals, saving you from worrying about unforeseen costs."
              }
            />
            <InfoCard
              title={"You gain from rapid and easy ramp-up"}
              description={
                "It takes little time to set up a dedicated software development team from Valueans because our IT specialists have already worked together successfully on similar projects and can concentrate on swiftly integrating into your development process, no matter how specific it is. Additionally, our team members are amiable and helpful, which facilitates their easy integration with other teams. "
              }
            />
          </div>
          <div className="flex flex-col md:flex-row justify-center md:justify-between  items-center gap-3 md:gap-6">
            <InfoCard
              title={"No need to adjust your processes to remote collaboration"}
              description={
                "It is not necessary for you to change your procedures to accommodate remote cooperation on your own. We can easily adjust to your current communication style and assist in configuring all the environments and tools required so that your team can work with our team without having to put in extra work."
              }
            />
            <InfoCard
              title={"Keep full technical ownership of the project"}
              description={
                "You retain complete technical control over the project and benefit from Valueans' experience.We impart to your internal team all project artifacts and information gained throughout the project, as well as our company's best practices. "
              }
            />
          </div>
          <div className="flex flex-col md:flex-row justify-center md:justify-between  items-center gap-3 md:gap-6">
            <InfoCard
              title={"No need to spend time on daily management."}
              description={
                "Without investing time in everyday management, you have influence over the outcomes.To guarantee process transparency and alignment with your objectives, our committed project manager oversees daily operations and processes and sends thorough reports to your project manager (or product owner) as appropriate. You may devote more time to the project's essential components in this way."
              }
            />
          </div>
        </div>
      </div>
    </section>
  );
};

export default Section5;
