import Image from "next/image";
import React, { Children } from "react";

const PointerCard = ({ children }) => {
  return (
    <div className="block w-full md:max-w-xl border border-pink-500  p-2 md:p-4 rounded-2xl shadow">
          <div className="w-full  flex items-start gap-1 mb-4">
            <div className="flex-shrink-0 w-6 h-6 sm:w-8 sm:h-8">
              <Image
                src="/Images/service_frame.png"
                alt="Tick"
                width={32}
                height={32}
                className="w-full h-full flex-shrink-0"
              />
            </div>
            <div className="w-[100%]">
              
              <p className="text-sm md:text-base text-justify">
                {children}
              </p>
            </div>
          </div>
        </div>
  );
};
const Purpleblock = ({ children }) => {
  return (
    <div className="bg-[#350668] w-full rounded-md md:w-[80%] mx-auto p-6">
      <p className="text-white text-base md:text-xl text-center">{children}</p>
    </div>
  );
};
const Section8 = () => {
  return (
    <section className="bg-blue-100 mb-10 md:mb-24 p-5 md:p-10">
      <div className="w-[85%] mx-auto">
        <h2 className="text-xl md:text-3xl md:leading-[40px] text-center font-semibold mb-1 md:mb-3">
          What This <span className="text-[#F245A1]">Data</span> Means for You
          as a <br /> Business
        </h2>
        <p className="d:w-[80%] md:mx-auto text-justify text-base md:text-xl mb-2 md:mb-5">
          The data confirms the importance of Product Strategy Consulting,
          Product Consulting Services, and Product Management Software
          Development for business success. Firms that adopt systematic product
          management practices:   
        </p>
        <p className="d:w-[70%] md:mx-auto text-base md:text-xl font-medium">
          Minimizing failure chances by taking care of strategic market needs
          and refining plans of action.
        </p>
        <div className="my-6 md:my-[42px] flex flex-col md:flex-row justify-center items-center gap-[16px] md:gap-[24px]">
          <PointerCard>
            Boost revenue growth by ensuring that products resonate with the
            business goals.
          </PointerCard>
          <PointerCard>
            Realizing user-centric design results in greater customer
            satisfaction.
          </PointerCard>
          <PointerCard>
            Execute <a href='#' class='text-[#7716BC] hover:underline'>AI-powered analytics and automation</a> to improve
            organizational effectiveness.
          </PointerCard>
        </div>
        <Purpleblock>
          These results are made possible at Valueans as we provide dedicated
          tailored Product Management Services that deliver real growth.
        </Purpleblock>
      </div>
    </section>
  );
};

export default Section8;
