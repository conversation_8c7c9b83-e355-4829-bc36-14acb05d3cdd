import React from "react";
import Image from "next/image";
import Button from "../Buttons/Button";

const Section1 = ({ backgroundImage, heading, bannerText }) => {
  return (
    <section
      className="relative min-h-screen md:min-h-screen"
      style={{
        backgroundImage: `url(${backgroundImage})`,
        backgroundSize: "cover",
        backgroundPosition: "center",
      }}
    >
      {/* Overlay div */}
      <div className="absolute inset-0 bg-[rgba(119,22,188,0.6)] z-0"></div>

      {/* Content */}
      <div className="flex flex-col md:flex-row justify-center md:items-center py-8 md:py-0 md:my-auto h-full relative z-10">
        <div className="flex flex-col items-start w-full md:w-[50%] gap-3 px-8 mb-6 md:mb-0">
          <h1 className="font-titillium text-3xl md:mb-4 font-bold md:text-5xl text-white ">
            {heading}
          </h1>

          {/* Full width h3 */}
          <h3 className="w-full font-semibold font-titillium text-lg md:text-2xl py-3 md:py-4 text-white">
            {bannerText}
          </h3>
          <Button bgColor="bg-[#F245A1]" hoverColor="opacity-90">
            Book your Consultation
          </Button>
        </div>
        <div className="w-full md:w-[50%] px-8">
          <form action="" className="flex flex-col gap-4 bg-[#350668] p-4 md:p-8 rounded-md">
          <input type="text" className="p-1 rounded-sm" name="" id="" placeholder="Name" />
          <input type="email" className="p-1 rounded-sm" name="" id="" placeholder="Email" />
          <input type="phone" className="p-1 rounded-sm" name="" id="" placeholder="Phone Number" />
            <textarea name="" id="" className="p-1 rounded-sm mb-8" rows={4} placeholder="Message"></textarea>
            <Button bgColor="bg-[#F245A1]" hoverColor="opacity-90">
            Send Message
          </Button>
          </form>
        </div>
      </div>
    </section>
  );
};

export default Section1;
